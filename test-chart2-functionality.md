# 各检测室检测量排名按钮切换功能修复

## 问题描述
各检测室检测量排名下面的echarts默认选中总量按钮，可以切换现在不同按钮调不同接口，需要传type，现在按钮切换不好使。

## 修复内容

### 1. 模板修改
- 为按钮添加了动态样式绑定和点击事件
- 使用 `chart2TypeMode` 状态控制按钮的选中状态
- 添加了 `@click="switchChart2Type('total')"` 和 `@click="switchChart2Type('positive')"` 事件

### 2. 数据属性添加
- 添加了 `chart2TypeMode: 'total'` 状态变量，默认选中总量

### 3. 方法实现
- `switchChart2Type(mode)`: 切换按钮类型并重新获取数据
- `getChart2Data()`: 根据当前模式调用对应的接口
- `getCount4Data()`: 获取总量数据（调用 /api/index/count4 接口）
- `getCount3Data()`: 获取阳性数据（调用 /api/index/count3 接口）
- `refreshChart2()`: 刷新图表显示

### 4. 接口调用
- 总量按钮：调用 `getCount4` 接口 (`/api/index/count4`)
- 阳性按钮：调用 `getCount3` 接口 (`/api/index/count3`)
- 两个接口都会传递 `type` 参数（统计周期：year/month/day）

### 5. 初始化修改
- 修改 `mounted` 方法，默认加载总量数据
- 修改 `reloadStatisticType` 方法，在统计周期变化时重新加载当前选中的数据

## 功能特点
1. 默认选中总量按钮
2. 按钮切换时会重新调用对应的接口
3. 支持统计周期切换（年/月/日）
4. 图表会根据数据自动刷新
5. 错误处理和日志记录

## 使用方式
用户可以点击"总量"或"阳性"按钮来切换不同的数据视图，系统会自动调用对应的接口并更新图表显示。

## 修改的具体文件位置

### 1. 模板部分 (第117-136行)
```vue
<div style="float: right; margin: 10px">
  <el-button :type="chart2TypeMode === 'total' ? 'primary' : 'primary'"
    :plain="chart2TypeMode !== 'total'"
    size="mini"
    @click="switchChart2Type('total')">
    总量
  </el-button>
  <el-button :type="chart2TypeMode === 'positive' ? 'primary' : 'primary'"
    :plain="chart2TypeMode !== 'positive'"
    size="mini"
    @click="switchChart2Type('positive')">
    阳性
  </el-button>
</div>
```

### 2. 数据属性 (第288行)
```javascript
chart2TypeMode: 'total', // 各检测室检测量排名模式：total-总量，positive-阳性
```

### 3. 接口导入 (第244行)
```javascript
import {
  getCount1, getCount2, getCount3, getCount4,
  // ... 其他导入
} from "@/api/wei";
```

### 4. 新增方法 (第1248-1384行)
- `switchChart2Type(mode)`: 切换按钮类型
- `getChart2Data()`: 获取数据的统一入口
- `getCount4Data()`: 获取总量数据
- `getCount3Data()`: 获取阳性数据
- `refreshChart2()`: 刷新图表

### 5. 初始化修改
- `mounted` 方法：调用 `getChart2Data()` 替代原来的 `getCount2()`
- `reloadStatisticType` 方法：添加 `getChart2Data()` 调用

## 测试建议
1. 页面加载时应该默认显示总量数据，总量按钮为选中状态
2. 点击阳性按钮应该切换到阳性数据，按钮状态相应改变
3. 切换统计周期（年/月/日）时，当前选中的按钮类型应该保持不变
4. 检查浏览器控制台是否有错误信息
5. 验证接口调用是否正确传递了type参数
