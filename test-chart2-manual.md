# 各检测室检测量排名按钮切换功能 - 手动测试指南

## 测试步骤

### 1. 页面加载测试
- [ ] 打开页面，检查各检测室检测量排名区域
- [ ] 确认"总量"按钮默认为选中状态（非plain样式）
- [ ] 确认"阳性"按钮为未选中状态（plain样式）
- [ ] 检查图表是否正常显示数据

### 2. 按钮切换测试
- [ ] 点击"阳性"按钮
  - 按钮状态应该切换（阳性选中，总量未选中）
  - 图表数据应该更新
  - 控制台应该显示"各检测室检测量排名-阳性数据"日志
- [ ] 点击"总量"按钮
  - 按钮状态应该切换回来（总量选中，阳性未选中）
  - 图表数据应该更新
  - 控制台应该显示"各检测室检测量排名-总量数据"日志

### 3. 统计周期切换测试
- [ ] 选择"总量"按钮状态下，切换统计周期（年/月/日）
  - 图表数据应该更新
  - 按钮状态保持"总量"选中
- [ ] 选择"阳性"按钮状态下，切换统计周期（年/月/日）
  - 图表数据应该更新
  - 按钮状态保持"阳性"选中

### 4. 网络请求测试
打开浏览器开发者工具的Network标签页：
- [ ] 点击"总量"按钮，应该看到对 `/api/index/count4` 的请求
- [ ] 点击"阳性"按钮，应该看到对 `/api/index/count3` 的请求
- [ ] 请求参数中应该包含正确的 `type` 值（year/month/day）

### 5. 错误处理测试
- [ ] 如果接口返回错误，检查控制台是否有相应的错误日志
- [ ] 图表应该能够处理空数据或异常数据

## 预期结果

### 接口调用
- 总量按钮：调用 `GET /api/index/count4?type=year` (默认)
- 阳性按钮：调用 `GET /api/index/count3?type=year` (默认)

### 数据格式
接口应该返回类似以下格式的数据：
```json
{
  "success": true,
  "data": [
    {
      "rwmc": "快检室1",
      "rwsl": 120
    },
    {
      "rwmc": "快检室2", 
      "rwsl": 89
    }
  ]
}
```

### 图表显示
- Y轴：显示检测室名称（rwmc字段）
- X轴：显示数量值（rwsl字段）
- 柱状图颜色：#3f95c2

## 常见问题排查

### 1. 按钮点击无反应
- 检查控制台是否有JavaScript错误
- 确认 `switchChart2Type` 方法是否正确定义
- 检查 `chart2TypeMode` 数据属性是否存在

### 2. 接口调用失败
- 检查网络请求是否正常发出
- 确认接口地址是否正确
- 检查请求参数是否包含 `type`

### 3. 图表不更新
- 检查 `refreshChart2` 方法是否被调用
- 确认 `this.chart2` 数据是否正确更新
- 检查ECharts实例是否正常获取

### 4. 数据显示异常
- 检查接口返回的数据格式是否符合预期
- 确认数据映射逻辑是否正确
- 检查是否有数据为空的情况
